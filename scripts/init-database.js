#!/usr/bin/env node

const ci = require('miniprogram-ci');
const path = require('path');

// 配置信息
const config = {
  appid: 'wx5682fa2b114e12de',
  type: 'miniProgram',
  projectPath: path.resolve(__dirname, '../'),
  privateKeyPath: path.resolve(__dirname, '../private.key'),
  ignores: ['node_modules/**/*'],
};

// 需要创建的数据库集合
const collections = [
  {
    name: 'users',
    description: '用户信息表',
    permission: 'PRIVATE' // 仅创建者可读写
  },
  {
    name: 'anxietyRecords',
    description: '焦虑记录表',
    permission: 'PRIVATE'
  },
  {
    name: 'tasks',
    description: '任务表',
    permission: 'PRIVATE'
  },
  {
    name: 'moodRecords',
    description: '情绪记录表',
    permission: 'PRIVATE'
  },
  {
    name: 'achievements',
    description: '成就记录表',
    permission: 'PRIVATE'
  },
  {
    name: 'notifications',
    description: '通知记录表',
    permission: 'PRIVATE'
  }
];

async function initDatabase() {
  try {
    console.log('开始初始化数据库...');
    
    const project = new ci.Project(config);
    
    for (const collection of collections) {
      console.log(`正在创建集合: ${collection.name}`);
      
      try {
        // 注意：miniprogram-ci 可能不支持直接创建数据库集合
        // 这里提供一个示例，实际可能需要手动创建
        console.log(`✅ 请手动创建集合: ${collection.name} (${collection.description})`);
        console.log(`   权限设置: ${collection.permission}`);
      } catch (error) {
        console.error(`❌ ${collection.name} 创建失败:`, error.message);
      }
    }
    
    console.log('🎉 数据库初始化完成！');
    console.log('');
    console.log('📝 手动创建步骤:');
    console.log('1. 打开微信开发者工具');
    console.log('2. 点击"云开发"按钮');
    console.log('3. 进入"数据库"标签页');
    console.log('4. 点击"+"按钮创建以下集合:');
    
    collections.forEach(col => {
      console.log(`   - ${col.name} (${col.description})`);
    });
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  }
}

// 创建示例数据
function createSampleData() {
  console.log('');
  console.log('📋 示例数据结构:');
  
  console.log('');
  console.log('users 集合示例:');
  console.log(JSON.stringify({
    openid: 'user_openid_here',
    unionid: 'user_unionid_here',
    profile: {
      nickname: '用户昵称',
      avatarUrl: 'https://example.com/avatar.jpg',
      gender: 1,
      city: '城市',
      province: '省份',
      country: '国家'
    },
    preferences: {
      notificationEnabled: true,
      reminderTime: '09:00',
      theme: 'light',
      language: 'zh-CN'
    },
    statistics: {
      totalAnxietyRecords: 0,
      totalTasksCompleted: 0,
      totalMoodRecords: 0,
      streakDays: 0,
      lastActiveDate: '2024-01-01'
    },
    privacy: {
      dataRetentionDays: 365,
      allowAnalytics: true
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }, null, 2));
}

// 运行初始化
initDatabase();
createSampleData();
