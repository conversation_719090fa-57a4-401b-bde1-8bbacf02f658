// 用户数据云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { action, ...data } = event;
  const wxContext = cloud.getWXContext();
  
  try {
    switch (action) {
      case 'getStats':
        return await getUserStats(data, wxContext);
      case 'getDashboard':
        return await getUserDashboard(data, wxContext);
      case 'updatePreferences':
        return await updateUserPreferences(data, wxContext);
      case 'getProfile':
        return await getUserProfile(data, wxContext);
      case 'updateProfile':
        return await updateUserProfile(data, wxContext);
      case 'exportData':
        return await exportUserData(data, wxContext);
      case 'deleteAccount':
        return await deleteUserAccount(data, wxContext);
      case 'getAchievements':
        return await getUserAchievements(data, wxContext);
      case 'getSettings':
        return await getUserSettings(data, wxContext);
      case 'updateSettings':
        return await updateUserSettings(data, wxContext);
      case 'deleteAllData':
        return await deleteUserAccount(data, wxContext);
      default:
        return { code: 1001, message: '不支持的操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { code: 5001, message: '系统内部错误' };
  }
};

// 获取用户统计数据
async function getUserStats(data, wxContext) {
  const { OPENID } = wxContext;
  
  try {
    // 获取用户基础信息
    const usersCollection = db.collection('users');
    const userResult = await usersCollection.where({
      openid: OPENID
    }).get();
    
    if (userResult.data.length === 0) {
      return { code: 1002, message: '用户不存在' };
    }
    
    const user = userResult.data[0];
    
    // 获取各种数据统计
    const [
      tasksStats,
      moodStats,
      anxietyStats,
      achievementStats
    ] = await Promise.all([
      getTasksStats(OPENID),
      getMoodStats(OPENID),
      getAnxietyStats(OPENID),
      getAchievementStats(OPENID)
    ]);
    
    // 计算连续使用天数
    const streakDays = await calculateStreakDays(OPENID);
    
    const stats = {
      // 基础统计
      totalAnxietyRecords: anxietyStats.total,
      totalTasksCompleted: tasksStats.completed,
      totalMoodRecords: moodStats.total,
      streakDays: streakDays,
      
      // 详细统计
      tasks: tasksStats,
      mood: moodStats,
      anxiety: anxietyStats,
      achievements: achievementStats,
      
      // 用户信息
      joinDate: user.createdAt,
      lastActiveDate: user.statistics?.lastActiveDate || user.updatedAt
    };
    
    return {
      code: 0,
      message: '获取成功',
      data: stats
    };
    
  } catch (error) {
    console.error('获取用户统计失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 获取用户仪表板数据
async function getUserDashboard(data, wxContext) {
  const { OPENID } = wxContext;
  
  try {
    // 获取今日数据
    const today = new Date().toISOString().split('T')[0];
    
    const [
      todayTasks,
      todayMood,
      recentAchievements,
      weeklyProgress
    ] = await Promise.all([
      getTodayTasks(OPENID),
      getTodayMood(OPENID, today),
      getRecentAchievements(OPENID),
      getWeeklyProgress(OPENID)
    ]);
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        today: {
          tasks: todayTasks,
          mood: todayMood,
          date: today
        },
        recentAchievements: recentAchievements,
        weeklyProgress: weeklyProgress
      }
    };
    
  } catch (error) {
    console.error('获取用户仪表板失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 更新用户偏好设置
async function updateUserPreferences(data, wxContext) {
  const { OPENID } = wxContext;
  const { preferences } = data;
  
  try {
    const usersCollection = db.collection('users');
    
    await usersCollection.where({
      openid: OPENID
    }).update({
      data: {
        preferences: preferences,
        updatedAt: new Date()
      }
    });
    
    return {
      code: 0,
      message: '设置更新成功',
      data: preferences
    };
    
  } catch (error) {
    console.error('更新用户偏好失败:', error);
    return { code: 3001, message: '更新失败' };
  }
}

// 获取用户资料
async function getUserProfile(data, wxContext) {
  const { OPENID } = wxContext;
  
  try {
    const usersCollection = db.collection('users');
    const userResult = await usersCollection.where({
      openid: OPENID
    }).get();
    
    if (userResult.data.length === 0) {
      return { code: 1002, message: '用户不存在' };
    }
    
    const user = userResult.data[0];
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        profile: user.profile,
        preferences: user.preferences,
        statistics: user.statistics,
        joinDate: user.createdAt
      }
    };
    
  } catch (error) {
    console.error('获取用户资料失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 更新用户资料
async function updateUserProfile(data, wxContext) {
  const { OPENID } = wxContext;
  const { profile } = data;
  
  try {
    const usersCollection = db.collection('users');
    
    await usersCollection.where({
      openid: OPENID
    }).update({
      data: {
        profile: profile,
        updatedAt: new Date()
      }
    });
    
    return {
      code: 0,
      message: '资料更新成功',
      data: profile
    };
    
  } catch (error) {
    console.error('更新用户资料失败:', error);
    return { code: 3001, message: '更新失败' };
  }
}

// 导出用户数据
async function exportUserData(data, wxContext) {
  const { OPENID } = wxContext;
  
  try {
    // 获取用户所有数据
    const [
      userInfo,
      tasks,
      moodJournals,
      anxietyRecords,
      achievements
    ] = await Promise.all([
      db.collection('users').where({ openid: OPENID }).get(),
      db.collection('tasks').where({ userId: OPENID }).get(),
      db.collection('moodJournals').where({ userId: OPENID }).get(),
      db.collection('anxietyRecords').where({ userId: OPENID }).get(),
      db.collection('userAchievements').where({ userId: OPENID }).get()
    ]);
    
    const exportData = {
      user: userInfo.data[0],
      tasks: tasks.data,
      moodJournals: moodJournals.data,
      anxietyRecords: anxietyRecords.data.map(record => ({
        ...record,
        // 移除敏感的AI分析数据，只保留用户输入
        input: record.input,
        createdAt: record.createdAt
      })),
      achievements: achievements.data,
      exportedAt: new Date(),
      version: '1.0'
    };
    
    return {
      code: 0,
      message: '数据导出成功',
      data: exportData
    };
    
  } catch (error) {
    console.error('导出用户数据失败:', error);
    return { code: 3001, message: '导出失败' };
  }
}

// 删除用户账户
async function deleteUserAccount(data, wxContext) {
  const { OPENID } = wxContext;
  const { confirmText } = data;
  
  // 验证确认文本
  if (confirmText !== '确认删除我的账户') {
    return { code: 1001, message: '确认文本不正确' };
  }
  
  try {
    // 删除用户相关的所有数据
    await Promise.all([
      db.collection('users').where({ openid: OPENID }).remove(),
      db.collection('tasks').where({ userId: OPENID }).remove(),
      db.collection('moodJournals').where({ userId: OPENID }).remove(),
      db.collection('anxietyRecords').where({ userId: OPENID }).remove(),
      db.collection('userAchievements').where({ userId: OPENID }).remove(),
      db.collection('achievementCards').where({ userId: OPENID }).remove(),
      db.collection('subscriptions').where({ userId: OPENID }).remove()
    ]);
    
    return {
      code: 0,
      message: '账户删除成功'
    };
    
  } catch (error) {
    console.error('删除用户账户失败:', error);
    return { code: 3001, message: '删除失败' };
  }
}

// 获取任务统计
async function getTasksStats(userId) {
  try {
    const tasksCollection = db.collection('tasks');
    
    const [totalResult, completedResult, pendingResult] = await Promise.all([
      tasksCollection.where({ userId }).count(),
      tasksCollection.where({ userId, status: 'completed' }).count(),
      tasksCollection.where({ userId, status: 'pending' }).count()
    ]);
    
    return {
      total: totalResult.total,
      completed: completedResult.total,
      pending: pendingResult.total,
      completionRate: totalResult.total > 0 ? Math.round(completedResult.total / totalResult.total * 100) : 0
    };
  } catch (error) {
    console.error('获取任务统计失败:', error);
    return { total: 0, completed: 0, pending: 0, completionRate: 0 };
  }
}

// 获取心情统计
async function getMoodStats(userId) {
  try {
    const moodCollection = db.collection('moodJournals');
    const result = await moodCollection.where({ userId }).count();
    
    return {
      total: result.total
    };
  } catch (error) {
    console.error('获取心情统计失败:', error);
    return { total: 0 };
  }
}

// 获取焦虑统计
async function getAnxietyStats(userId) {
  try {
    const anxietyCollection = db.collection('anxietyRecords');
    const result = await anxietyCollection.where({ userId }).count();
    
    return {
      total: result.total
    };
  } catch (error) {
    console.error('获取焦虑统计失败:', error);
    return { total: 0 };
  }
}

// 获取成就统计
async function getAchievementStats(userId) {
  try {
    const achievementsCollection = db.collection('userAchievements');
    const result = await achievementsCollection.where({ userId }).get();
    
    const totalPoints = result.data.reduce((sum, achievement) => sum + (achievement.points || 0), 0);
    
    return {
      total: result.data.length,
      totalPoints: totalPoints
    };
  } catch (error) {
    console.error('获取成就统计失败:', error);
    return { total: 0, totalPoints: 0 };
  }
}

// 计算连续使用天数
async function calculateStreakDays(userId) {
  try {
    // 获取最近的活动记录（任务完成、心情记录等）
    const today = new Date();
    const activities = [];
    
    // 获取最近30天的活动
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      const [taskActivity, moodActivity] = await Promise.all([
        db.collection('tasks').where({
          userId,
          status: 'completed',
          'completion.completedAt': _.gte(new Date(dateStr)).and(_.lt(new Date(dateStr + 'T23:59:59')))
        }).count(),
        db.collection('moodJournals').where({
          userId,
          recordDate: dateStr
        }).count()
      ]);
      
      if (taskActivity.total > 0 || moodActivity.total > 0) {
        activities.push(dateStr);
      } else {
        break; // 连续记录中断
      }
    }
    
    return activities.length;
  } catch (error) {
    console.error('计算连续使用天数失败:', error);
    return 0;
  }
}

// 获取今日任务
async function getTodayTasks(userId) {
  try {
    const today = new Date().toISOString().split('T')[0];
    const tasksCollection = db.collection('tasks');
    
    const [totalResult, completedResult] = await Promise.all([
      tasksCollection.where({
        userId,
        dueDate: _.gte(new Date(today)).and(_.lt(new Date(today + 'T23:59:59')))
      }).count(),
      tasksCollection.where({
        userId,
        status: 'completed',
        'completion.completedAt': _.gte(new Date(today))
      }).count()
    ]);
    
    return {
      total: totalResult.total,
      completed: completedResult.total
    };
  } catch (error) {
    console.error('获取今日任务失败:', error);
    return { total: 0, completed: 0 };
  }
}

// 获取今日心情
async function getTodayMood(userId, date) {
  try {
    const moodCollection = db.collection('moodJournals');
    const result = await moodCollection.where({
      userId,
      recordDate: date
    }).get();
    
    return result.data.length > 0 ? result.data[0] : null;
  } catch (error) {
    console.error('获取今日心情失败:', error);
    return null;
  }
}

// 获取最近成就
async function getRecentAchievements(userId) {
  try {
    const achievementsCollection = db.collection('userAchievements');
    const result = await achievementsCollection.where({
      userId
    }).orderBy('unlockedAt', 'desc').limit(3).get();
    
    return result.data;
  } catch (error) {
    console.error('获取最近成就失败:', error);
    return [];
  }
}

// 获取周进度
async function getWeeklyProgress(userId) {
  try {
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const [tasksResult, moodResult] = await Promise.all([
      db.collection('tasks').where({
        userId,
        status: 'completed',
        'completion.completedAt': _.gte(weekAgo)
      }).count(),
      db.collection('moodJournals').where({
        userId,
        recordDate: _.gte(weekAgo.toISOString().split('T')[0])
      }).count()
    ]);

    return {
      tasksCompleted: tasksResult.total,
      moodRecords: moodResult.total
    };
  } catch (error) {
    console.error('获取周进度失败:', error);
    return { tasksCompleted: 0, moodRecords: 0 };
  }
}

// 获取用户成就
async function getUserAchievements(data, wxContext) {
  const { OPENID } = wxContext;

  try {
    const achievementsCollection = db.collection('userAchievements');
    const result = await achievementsCollection.where({
      userId: OPENID
    }).orderBy('unlockedAt', 'desc').get();

    // 模拟一些成就数据，实际应该从成就系统获取
    const mockAchievements = [
      { id: 1, name: '初次尝试', icon: '🌟', unlocked: true, description: '完成第一次焦虑倾诉' },
      { id: 2, name: '坚持不懈', icon: '💪', unlocked: result.data.length >= 5, description: '连续使用5天' },
      { id: 3, name: '任务达人', icon: '✅', unlocked: false, description: '完成100个任务' },
      { id: 4, name: '情绪管理师', icon: '😊', unlocked: false, description: '记录30天心情' },
      { id: 5, name: '焦虑克星', icon: '🎯', unlocked: false, description: '解决50个焦虑问题' }
    ];

    return {
      code: 0,
      message: '获取成功',
      data: mockAchievements
    };

  } catch (error) {
    console.error('获取用户成就失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 获取用户设置
async function getUserSettings(data, wxContext) {
  const { OPENID } = wxContext;

  try {
    const usersCollection = db.collection('users');
    const userResult = await usersCollection.where({
      openid: OPENID
    }).get();

    if (userResult.data.length === 0) {
      return { code: 1002, message: '用户不存在' };
    }

    const user = userResult.data[0];
    const defaultSettings = {
      notificationEnabled: true,
      theme: 'light',
      language: 'zh-CN',
      reminderTime: '09:00',
      dataRetentionDays: 365,
      allowAnalytics: true
    };

    return {
      code: 0,
      message: '获取成功',
      data: { ...defaultSettings, ...user.preferences }
    };

  } catch (error) {
    console.error('获取用户设置失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 更新用户设置
async function updateUserSettings(data, wxContext) {
  const { OPENID } = wxContext;
  const { settings } = data;

  try {
    const usersCollection = db.collection('users');

    await usersCollection.where({
      openid: OPENID
    }).update({
      data: {
        preferences: _.set(settings),
        updatedAt: new Date()
      }
    });

    return {
      code: 0,
      message: '设置更新成功',
      data: settings
    };

  } catch (error) {
    console.error('更新用户设置失败:', error);
    return { code: 3001, message: '更新失败' };
  }
}
