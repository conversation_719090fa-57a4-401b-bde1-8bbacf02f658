/* pages/task-detail/index.wxss */

.task-detail-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding-bottom: var(--spacing-xxl);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-animation {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-lg);
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: var(--primary-color);
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.loading-text {
  color: var(--text-secondary);
  font-size: var(--font-size-body-sm);
}

/* 表单样式 */
.task-form {
  padding: var(--page-padding);
}

.form-section {
  margin-bottom: var(--spacing-xl);
}

.form-section.half {
  flex: 1;
}

.form-row {
  display: flex;
  gap: var(--spacing-lg);
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.label-text {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.required {
  color: var(--error-color);
  margin-left: var(--spacing-xs);
}

.label-tip {
  font-size: var(--font-size-caption);
  color: var(--text-tertiary);
  margin-left: var(--spacing-sm);
}

.form-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-primary);
  border: 2rpx solid var(--bg-tertiary);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-input);
  color: var(--text-primary);
  transition: border-color 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
}

.form-input[disabled] {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-primary);
  border: 2rpx solid var(--bg-tertiary);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-input);
  color: var(--text-primary);
  transition: border-color 0.3s ease;
  line-height: 1.6;
}

.form-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
}

.form-textarea[disabled] {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-primary);
  border: 2rpx solid var(--bg-tertiary);
  border-radius: var(--border-radius-md);
  transition: border-color 0.3s ease;
}

.form-picker:active {
  border-color: var(--primary-color);
}

.picker-text {
  font-size: var(--font-size-input);
  color: var(--text-primary);
}

.picker-text.priority-high {
  color: var(--error-color);
}

.picker-text.priority-medium {
  color: var(--warning-color);
}

.picker-text.priority-low {
  color: var(--text-secondary);
}

.picker-arrow {
  font-size: 24rpx;
  color: var(--text-tertiary);
  font-weight: bold;
}

/* 提醒设置 */
.reminder-setting {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius-md);
}

.reminder-text {
  margin-left: var(--spacing-md);
  font-size: var(--font-size-body);
  color: var(--text-primary);
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border-top: 1rpx solid var(--bg-tertiary);
  padding: var(--spacing-lg) var(--page-padding);
  padding-bottom: calc(var(--spacing-lg) + env(safe-area-inset-bottom));
}

.button-group {
  display: flex;
  gap: var(--spacing-md);
}

.button-group .btn {
  flex: 1;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .form-row {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .form-section.half {
    flex: none;
  }
}

/* 安全区域适配 */
.safe-area-bottom {
  height: 120rpx;
  padding-bottom: env(safe-area-inset-bottom);
}
