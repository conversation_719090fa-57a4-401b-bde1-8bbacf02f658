<!--pages/task-detail/index.wxml-->
<view class="task-detail-page">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-animation">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 任务表单 -->
  <view class="task-form" wx:if="{{!isLoading}}">
    <!-- 任务标题 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">任务标题</text>
        <text class="required">*</text>
      </view>
      <input
        class="form-input"
        placeholder="请输入任务标题"
        value="{{formData.title}}"
        bindinput="onTitleInput"
        disabled="{{mode === 'view'}}"
        maxlength="50"
      />
    </view>

    <!-- 任务描述 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">任务描述</text>
      </view>
      <textarea
        class="form-textarea"
        placeholder="请输入任务描述（可选）"
        value="{{formData.description}}"
        bindinput="onDescriptionInput"
        disabled="{{mode === 'view'}}"
        maxlength="200"
        auto-height
      />
    </view>

    <!-- 分类和优先级 -->
    <view class="form-row">
      <view class="form-section half">
        <view class="form-label">
          <text class="label-text">分类</text>
        </view>
        <picker
          range="{{categoryOptions}}"
          value="{{categoryOptions.indexOf(formData.category)}}"
          bindchange="onCategoryChange"
          disabled="{{mode === 'view'}}"
        >
          <view class="form-picker">
            <text class="picker-text">{{formData.category}}</text>
            <text class="picker-arrow" wx:if="{{mode !== 'view'}}">›</text>
          </view>
        </picker>
      </view>

      <view class="form-section half">
        <view class="form-label">
          <text class="label-text">优先级</text>
        </view>
        <picker
          range="{{priorityOptions}}"
          range-key="label"
          value="{{priorityOptions.findIndex(item => item.value === formData.priority)}}"
          bindchange="onPriorityChange"
          disabled="{{mode === 'view'}}"
        >
          <view class="form-picker">
            <text class="picker-text priority-{{formData.priority}}">
              {{priorityOptions.find(item => item.value === formData.priority).label}}
            </text>
            <text class="picker-arrow" wx:if="{{mode !== 'view'}}">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 截止时间 -->
    <view class="form-row">
      <view class="form-section half">
        <view class="form-label">
          <text class="label-text">截止日期</text>
        </view>
        <picker
          mode="date"
          value="{{formData.dueDate}}"
          bindchange="onDateChange"
          disabled="{{mode === 'view'}}"
        >
          <view class="form-picker">
            <text class="picker-text">{{formData.dueDate || '选择日期'}}</text>
            <text class="picker-arrow" wx:if="{{mode !== 'view'}}">›</text>
          </view>
        </picker>
      </view>

      <view class="form-section half">
        <view class="form-label">
          <text class="label-text">截止时间</text>
        </view>
        <picker
          mode="time"
          value="{{formData.dueTime}}"
          bindchange="onTimeChange"
          disabled="{{mode === 'view'}}"
        >
          <view class="form-picker">
            <text class="picker-text">{{formData.dueTime || '选择时间'}}</text>
            <text class="picker-arrow" wx:if="{{mode !== 'view'}}">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 标签 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">标签</text>
        <text class="label-tip">用逗号分隔多个标签</text>
      </view>
      <input
        class="form-input"
        placeholder="如：重要,紧急,工作"
        value="{{formData.tags}}"
        bindinput="onTagsInput"
        disabled="{{mode === 'view'}}"
        maxlength="100"
      />
    </view>

    <!-- 提醒设置 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">提醒设置</text>
      </view>
      <view class="reminder-setting">
        <switch
          checked="{{formData.reminderEnabled}}"
          bindchange="onReminderToggle"
          disabled="{{mode === 'view'}}"
          color="#4A90E2"
        />
        <text class="reminder-text">开启任务提醒</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons" wx:if="{{!isLoading}}">
    <!-- 创建模式 -->
    <view wx:if="{{mode === 'create'}}" class="button-group">
      <button class="btn btn-secondary" bindtap="navigateBack">取消</button>
      <button
        class="btn btn-primary"
        bindtap="saveTask"
        loading="{{isSaving}}"
        disabled="{{isSaving}}"
      >
        {{isSaving ? '创建中...' : '创建任务'}}
      </button>
    </view>

    <!-- 编辑模式 -->
    <view wx:elif="{{mode === 'edit'}}" class="button-group">
      <button class="btn btn-secondary" bindtap="toggleEditMode">取消</button>
      <button
        class="btn btn-primary"
        bindtap="saveTask"
        loading="{{isSaving}}"
        disabled="{{isSaving}}"
      >
        {{isSaving ? '保存中...' : '保存更改'}}
      </button>
    </view>

    <!-- 查看模式 -->
    <view wx:else class="button-group">
      <button class="btn btn-secondary" bindtap="deleteTask">删除</button>
      <button class="btn btn-primary" bindtap="toggleEditMode">编辑</button>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>