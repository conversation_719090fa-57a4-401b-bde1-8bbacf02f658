<!--pages/profile/index.wxml-->
<view class="profile-page">
  <!-- 用户信息卡片 -->
  <view class="user-info-card">
    <view class="user-header">
      <view class="avatar-section">
        <image
          class="user-avatar"
          src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"
          mode="aspectFill"
          bindtap="editAvatar"
        />
        <view class="avatar-edit-btn" bindtap="editAvatar">
          <text class="edit-icon-text">✏️</text>
        </view>
      </view>

      <view class="user-details">
        <view class="user-name-section">
          <text class="user-name">{{userInfo.nickName || '未设置昵称'}}</text>
          <view class="edit-name-btn" bindtap="editNickname">
            <text class="edit-icon-small-text">✏️</text>
          </view>
        </view>
        <text class="user-desc">{{userInfo.desc || '还没有个人简介'}}</text>
        <view class="user-stats">
          <view class="stat-item">
            <text class="stat-number">{{stats.streakDays || 0}}</text>
            <text class="stat-label">连续天数</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{stats.totalTasksCompleted || 0}}</text>
            <text class="stat-label">完成任务</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{stats.totalAnxietyRecords || 0}}</text>
            <text class="stat-label">倾诉次数</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 成就展示 -->
  <view class="achievement-section" wx:if="{{achievements.length > 0}}">
    <view class="section-header">
      <text class="section-title">我的成就</text>
      <text class="section-more" bindtap="viewAllAchievements">查看全部</text>
    </view>
    <scroll-view class="achievement-scroll" scroll-x="true">
      <view class="achievement-list">
        <view
          class="achievement-item {{item.unlocked ? 'unlocked' : 'locked'}}"
          wx:for="{{achievements}}"
          wx:key="id"
          bindtap="viewAchievement"
          data-achievement="{{item}}"
        >
          <view class="achievement-icon">
            <text class="achievement-emoji">{{item.icon}}</text>
          </view>
          <text class="achievement-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <!-- 数据统计 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="viewDataStats">
        <view class="menu-icon">
          <text class="icon-text">📊</text>
        </view>
        <view class="menu-content">
          <text class="menu-title">数据统计</text>
          <text class="menu-desc">查看详细的使用数据和趋势</text>
        </view>
        <view class="menu-arrow">
          <text class="arrow-text">›</text>
        </view>
      </view>
    </view>

    <!-- 设置选项 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="openNotificationSettings">
        <view class="menu-icon">
          <text class="icon-text">🔔</text>
        </view>
        <view class="menu-content">
          <text class="menu-title">通知设置</text>
          <text class="menu-desc">管理提醒和通知偏好</text>
        </view>
        <view class="menu-toggle">
          <switch
            checked="{{settings.notificationEnabled}}"
            bindchange="toggleNotification"
            color="{{primaryColor}}"
          />
        </view>
      </view>

      <view class="menu-item" bindtap="openThemeSettings">
        <view class="menu-icon">
          <text class="icon-text">🎨</text>
        </view>
        <view class="menu-content">
          <text class="menu-title">主题设置</text>
          <text class="menu-desc">{{settings.theme === 'dark' ? '深色模式' : '浅色模式'}}</text>
        </view>
        <view class="menu-arrow">
          <text class="arrow-text">›</text>
        </view>
      </view>

      <view class="menu-item" bindtap="openPrivacySettings">
        <view class="menu-icon">
          <text class="icon-text">🔒</text>
        </view>
        <view class="menu-content">
          <text class="menu-title">隐私设置</text>
          <text class="menu-desc">数据保护和隐私控制</text>
        </view>
        <view class="menu-arrow">
          <text class="arrow-text">›</text>
        </view>
      </view>
    </view>

    <!-- 数据管理 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="exportData">
        <view class="menu-icon">
          <text class="icon-text">📤</text>
        </view>
        <view class="menu-content">
          <text class="menu-title">导出数据</text>
          <text class="menu-desc">导出个人数据备份</text>
        </view>
        <view class="menu-arrow">
          <text class="arrow-text">›</text>
        </view>
      </view>

      <view class="menu-item danger" bindtap="showDeleteDataModal">
        <view class="menu-icon">
          <text class="icon-text danger-icon">🗑️</text>
        </view>
        <view class="menu-content">
          <text class="menu-title danger-text">删除数据</text>
          <text class="menu-desc">永久删除所有个人数据</text>
        </view>
        <view class="menu-arrow">
          <text class="arrow-text">›</text>
        </view>
      </view>
    </view>

    <!-- 帮助与反馈 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="openHelp">
        <view class="menu-icon">
          <text class="icon-text">❓</text>
        </view>
        <view class="menu-content">
          <text class="menu-title">帮助中心</text>
          <text class="menu-desc">使用指南和常见问题</text>
        </view>
        <view class="menu-arrow">
          <text class="arrow-text">›</text>
        </view>
      </view>

      <view class="menu-item" bindtap="openFeedback">
        <view class="menu-icon">
          <text class="icon-text">💬</text>
        </view>
        <view class="menu-content">
          <text class="menu-title">意见反馈</text>
          <text class="menu-desc">告诉我们您的建议</text>
        </view>
        <view class="menu-arrow">
          <text class="arrow-text">›</text>
        </view>
      </view>

      <view class="menu-item" bindtap="openAbout">
        <view class="menu-icon">
          <text class="icon-text">ℹ️</text>
        </view>
        <view class="menu-content">
          <text class="menu-title">关于心安AI</text>
          <text class="menu-desc">版本信息和开发团队</text>
        </view>
        <view class="menu-arrow">
          <text class="arrow-text">›</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 编辑昵称弹窗 -->
<view class="modal-overlay {{showNicknameModal ? 'show' : ''}}" bindtap="closeNicknameModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">编辑昵称</text>
      <view class="modal-close" bindtap="closeNicknameModal">
        <text class="close-text">✕</text>
      </view>
    </view>
    <view class="modal-body">
      <input
        class="nickname-input"
        placeholder="请输入昵称"
        value="{{tempNickname}}"
        bindinput="onNicknameInput"
        maxlength="20"
      />
      <text class="input-tip">昵称长度不超过20个字符</text>
    </view>
    <view class="modal-footer">
      <button class="btn btn-secondary" bindtap="closeNicknameModal">取消</button>
      <button class="btn btn-primary" bindtap="saveNickname">保存</button>
    </view>
  </view>
</view>

<!-- 删除数据确认弹窗 -->
<view class="modal-overlay {{showDeleteModal ? 'show' : ''}}" bindtap="closeDeleteModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">删除数据</text>
    </view>
    <view class="modal-body">
      <text class="warning-text">此操作将永久删除您的所有数据，包括：</text>
      <view class="warning-list">
        <text class="warning-item">• 所有焦虑记录和任务</text>
        <text class="warning-item">• 情绪日记和统计数据</text>
        <text class="warning-item">• 个人设置和偏好</text>
      </view>
      <text class="warning-text">此操作不可恢复，请谨慎操作！</text>
    </view>
    <view class="modal-footer">
      <button class="btn btn-secondary" bindtap="closeDeleteModal">取消</button>
      <button class="btn btn-error" bindtap="confirmDeleteData">确认删除</button>
    </view>
  </view>
</view>