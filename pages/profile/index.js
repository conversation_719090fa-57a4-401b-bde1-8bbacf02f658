// pages/profile/index.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    stats: {
      streakDays: 0,
      totalTasksCompleted: 0,
      totalAnxietyRecords: 0,
      totalMoodRecords: 0
    },
    achievements: [],
    settings: {
      notificationEnabled: true,
      theme: 'light',
      language: 'zh-CN'
    },
    primaryColor: '#4A90E2',

    // 弹窗状态
    showNicknameModal: false,
    showDeleteModal: false,
    tempNickname: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.loadUserData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示时刷新数据
    this.loadUserData();
  },

  /**
   * 加载用户数据
   */
  async loadUserData() {
    try {
      app.showLoading('加载中...');

      // 获取用户基本信息
      const userInfo = app.globalData.userInfo || {};

      // 如果没有用户信息，设置默认值
      if (!userInfo.nickName) {
        userInfo.nickName = '心安用户';
        userInfo.avatarUrl = '/images/default-avatar.png';
        userInfo.desc = '让焦虑变成行动力';
      }

      this.setData({ userInfo });

      // 加载统计数据
      await this.loadUserStats();

      // 加载成就数据
      await this.loadAchievements();

      // 加载设置
      await this.loadSettings();

      app.hideLoading();
    } catch (error) {
      console.error('加载用户数据失败:', error);
      app.hideLoading();

      // 设置默认数据以确保页面能正常显示
      this.setDefaultData();
      app.showError('加载失败，使用离线模式');
    }
  },

  /**
   * 设置默认数据
   */
  setDefaultData() {
    this.setData({
      userInfo: {
        nickName: '心安用户',
        avatarUrl: '/images/default-avatar.png',
        desc: '让焦虑变成行动力'
      },
      stats: {
        streakDays: 0,
        totalTasksCompleted: 0,
        totalAnxietyRecords: 0,
        totalMoodRecords: 0
      },
      achievements: [
        { id: 1, name: '初次尝试', icon: '🌟', unlocked: true },
        { id: 2, name: '坚持不懈', icon: '💪', unlocked: false },
        { id: 3, name: '任务达人', icon: '✅', unlocked: false }
      ],
      settings: {
        notificationEnabled: true,
        theme: 'light',
        language: 'zh-CN'
      }
    });
  },

  /**
   * 加载用户统计数据
   */
  async loadUserStats() {
    try {
      // 检查是否已登录
      if (!app.globalData.isLoggedIn) {
        console.log('用户未登录，使用默认统计数据');
        this.setData({
          stats: {
            streakDays: 0,
            totalTasksCompleted: 0,
            totalAnxietyRecords: 0,
            totalMoodRecords: 0
          }
        });
        return;
      }

      const res = await wx.cloud.callFunction({
        name: 'user',
        data: { action: 'getStats' }
      });

      if (res.result && res.result.code === 0) {
        this.setData({
          stats: res.result.data
        });
      } else {
        throw new Error(res.result?.message || '获取统计数据失败');
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 设置默认统计数据
      this.setData({
        stats: {
          streakDays: 0,
          totalTasksCompleted: 0,
          totalAnxietyRecords: 0,
          totalMoodRecords: 0
        }
      });
    }
  },

  /**
   * 加载成就数据
   */
  async loadAchievements() {
    try {
      // 检查是否已登录
      if (!app.globalData.isLoggedIn) {
        console.log('用户未登录，使用默认成就数据');
        this.setDefaultAchievements();
        return;
      }

      const res = await wx.cloud.callFunction({
        name: 'user',
        data: { action: 'getAchievements' }
      });

      if (res.result && res.result.code === 0) {
        // 只显示前5个成就
        const achievements = res.result.data.slice(0, 5);
        this.setData({ achievements });
      } else {
        throw new Error(res.result?.message || '获取成就数据失败');
      }
    } catch (error) {
      console.error('加载成就数据失败:', error);
      // 设置默认成就数据
      this.setDefaultAchievements();
    }
  },

  /**
   * 设置默认成就数据
   */
  setDefaultAchievements() {
    this.setData({
      achievements: [
        { id: 1, name: '初次尝试', icon: '🌟', unlocked: true, description: '开始使用心安AI' },
        { id: 2, name: '坚持不懈', icon: '💪', unlocked: false, description: '连续使用5天' },
        { id: 3, name: '任务达人', icon: '✅', unlocked: false, description: '完成10个任务' },
        { id: 4, name: '情绪管理师', icon: '😊', unlocked: false, description: '记录7天心情' },
        { id: 5, name: '焦虑克星', icon: '🎯', unlocked: false, description: '完成5次倾诉' }
      ]
    });
  },

  /**
   * 加载用户设置
   */
  async loadSettings() {
    try {
      // 检查是否已登录
      if (!app.globalData.isLoggedIn) {
        console.log('用户未登录，使用默认设置');
        this.setDefaultSettings();
        return;
      }

      const res = await wx.cloud.callFunction({
        name: 'user',
        data: { action: 'getSettings' }
      });

      if (res.result && res.result.code === 0) {
        this.setData({
          settings: res.result.data
        });
      } else {
        throw new Error(res.result?.message || '获取设置失败');
      }
    } catch (error) {
      console.error('加载设置失败:', error);
      // 使用默认设置
      this.setDefaultSettings();
    }
  },

  /**
   * 设置默认设置
   */
  setDefaultSettings() {
    this.setData({
      settings: {
        notificationEnabled: true,
        theme: 'light',
        language: 'zh-CN',
        reminderTime: '09:00',
        dataRetentionDays: 365,
        allowAnalytics: true
      }
    });
  },

  /**
   * 编辑头像
   */
  editAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.uploadAvatar(tempFilePath);
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        app.showError('选择图片失败');
      }
    });
  },

  /**
   * 上传头像
   */
  async uploadAvatar(filePath) {
    try {
      app.showLoading('上传中...');

      // 上传到云存储
      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: `avatars/${Date.now()}-${Math.random().toString(36).substring(2, 11)}.jpg`,
        filePath: filePath
      });

      // 更新用户信息
      const res = await wx.cloud.callFunction({
        name: 'auth',
        data: {
          action: 'updateProfile',
          userInfo: {
            avatarUrl: uploadRes.fileID
          }
        }
      });

      if (res.result.code === 0) {
        // 更新本地数据
        const userInfo = { ...this.data.userInfo, avatarUrl: uploadRes.fileID };
        this.setData({ userInfo });

        // 更新全局数据
        app.globalData.userInfo = userInfo;
        wx.setStorageSync('userInfo', userInfo);

        app.hideLoading();
        app.showSuccess('头像更新成功');
      } else {
        throw new Error(res.result.message);
      }
    } catch (error) {
      console.error('上传头像失败:', error);
      app.hideLoading();
      app.showError('上传失败，请重试');
    }
  },

  /**
   * 编辑昵称
   */
  editNickname() {
    this.setData({
      showNicknameModal: true,
      tempNickname: this.data.userInfo.nickName || ''
    });
  },

  /**
   * 昵称输入
   */
  onNicknameInput(e) {
    this.setData({
      tempNickname: e.detail.value
    });
  },

  /**
   * 保存昵称
   */
  async saveNickname() {
    const nickname = this.data.tempNickname.trim();

    if (!nickname) {
      app.showError('请输入昵称');
      return;
    }

    if (nickname.length > 20) {
      app.showError('昵称长度不能超过20个字符');
      return;
    }

    try {
      app.showLoading('保存中...');

      const res = await wx.cloud.callFunction({
        name: 'auth',
        data: {
          action: 'updateProfile',
          userInfo: {
            nickName: nickname
          }
        }
      });

      if (res.result.code === 0) {
        // 更新本地数据
        const userInfo = { ...this.data.userInfo, nickName: nickname };
        this.setData({
          userInfo,
          showNicknameModal: false
        });

        // 更新全局数据
        app.globalData.userInfo = userInfo;
        wx.setStorageSync('userInfo', userInfo);

        app.hideLoading();
        app.showSuccess('昵称更新成功');
      } else {
        throw new Error(res.result.message);
      }
    } catch (error) {
      console.error('保存昵称失败:', error);
      app.hideLoading();
      app.showError('保存失败，请重试');
    }
  },

  /**
   * 关闭昵称弹窗
   */
  closeNicknameModal() {
    this.setData({
      showNicknameModal: false,
      tempNickname: ''
    });
  },

  /**
   * 查看所有成就
   */
  viewAllAchievements() {
    wx.navigateTo({
      url: '/pages/achievements/index'
    });
  },

  /**
   * 查看单个成就
   */
  viewAchievement(e) {
    const achievement = e.currentTarget.dataset.achievement;
    wx.showModal({
      title: achievement.name,
      content: achievement.description,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 查看数据统计
   */
  viewDataStats() {
    app.showError('数据统计功能开发中');
    // wx.navigateTo({
    //   url: '/pages/data-stats/index'
    // });
  },

  /**
   * 打开通知设置
   */
  openNotificationSettings() {
    app.showError('通知设置功能开发中');
    // wx.navigateTo({
    //   url: '/pages/notification-settings/index'
    // });
  },

  /**
   * 切换通知开关
   */
  async toggleNotification(e) {
    const enabled = e.detail.value;

    try {
      const res = await wx.cloud.callFunction({
        name: 'user',
        data: {
          action: 'updateSettings',
          settings: {
            notificationEnabled: enabled
          }
        }
      });

      if (res.result.code === 0) {
        this.setData({
          'settings.notificationEnabled': enabled
        });

        if (enabled) {
          // 请求订阅消息权限
          this.requestSubscribeMessage();
        }

        app.showSuccess('设置已更新');
      } else {
        throw new Error(res.result.message);
      }
    } catch (error) {
      console.error('更新通知设置失败:', error);
      app.showError('设置失败，请重试');

      // 恢复开关状态
      this.setData({
        'settings.notificationEnabled': !enabled
      });
    }
  },

  /**
   * 请求订阅消息权限
   */
  requestSubscribeMessage() {
    wx.requestSubscribeMessage({
      tmplIds: ['your_template_id'], // 替换为实际的模板ID
      success: (res) => {
        console.log('订阅消息授权成功:', res);
      },
      fail: (error) => {
        console.error('订阅消息授权失败:', error);
      }
    });
  },

  /**
   * 打开主题设置
   */
  openThemeSettings() {
    wx.showActionSheet({
      itemList: ['浅色模式', '深色模式', '跟随系统'],
      success: (res) => {
        const themes = ['light', 'dark', 'auto'];
        const selectedTheme = themes[res.tapIndex];
        this.updateTheme(selectedTheme);
      }
    });
  },

  /**
   * 更新主题
   */
  async updateTheme(theme) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'user',
        data: {
          action: 'updateSettings',
          settings: {
            theme: theme
          }
        }
      });

      if (res.result.code === 0) {
        this.setData({
          'settings.theme': theme
        });
        app.showSuccess('主题设置已更新');
      } else {
        throw new Error(res.result.message);
      }
    } catch (error) {
      console.error('更新主题失败:', error);
      app.showError('设置失败，请重试');
    }
  },

  /**
   * 打开隐私设置
   */
  openPrivacySettings() {
    app.showError('隐私设置功能开发中');
    // wx.navigateTo({
    //   url: '/pages/privacy-settings/index'
    // });
  },

  /**
   * 导出数据
   */
  async exportData() {
    try {
      app.showLoading('导出中...');

      const res = await wx.cloud.callFunction({
        name: 'user',
        data: { action: 'exportData' }
      });

      if (res.result.code === 0) {
        const fileUrl = res.result.data.fileUrl;

        // 下载文件
        wx.downloadFile({
          url: fileUrl,
          success: (downloadRes) => {
            if (downloadRes.statusCode === 200) {
              // 保存到相册或分享
              wx.showModal({
                title: '导出成功',
                content: '数据已导出，是否保存到本地？',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    wx.saveFile({
                      tempFilePath: downloadRes.tempFilePath,
                      success: () => {
                        app.showSuccess('文件已保存');
                      },
                      fail: () => {
                        app.showError('保存失败');
                      }
                    });
                  }
                }
              });
            }
          },
          fail: () => {
            app.showError('下载失败');
          }
        });
      }

      app.hideLoading();
    } catch (error) {
      console.error('导出数据失败:', error);
      app.hideLoading();
      app.showError('导出失败，请重试');
    }
  },

  /**
   * 显示删除数据弹窗
   */
  showDeleteDataModal() {
    this.setData({
      showDeleteModal: true
    });
  },

  /**
   * 关闭删除数据弹窗
   */
  closeDeleteModal() {
    this.setData({
      showDeleteModal: false
    });
  },

  /**
   * 确认删除数据
   */
  async confirmDeleteData() {
    try {
      app.showLoading('删除中...');

      const res = await wx.cloud.callFunction({
        name: 'user',
        data: { action: 'deleteAllData' }
      });

      if (res.result.code === 0) {
        // 清除本地数据
        wx.clearStorageSync();
        app.globalData = {
          isLoggedIn: false,
          userInfo: {}
        };

        app.hideLoading();
        app.showSuccess('数据已删除');

        // 跳转到首页
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }, 1500);
      }
    } catch (error) {
      console.error('删除数据失败:', error);
      app.hideLoading();
      app.showError('删除失败，请重试');
    }
  },

  /**
   * 打开帮助中心
   */
  openHelp() {
    app.showError('帮助中心功能开发中');
    // wx.navigateTo({
    //   url: '/pages/help/index'
    // });
  },

  /**
   * 打开意见反馈
   */
  openFeedback() {
    app.showError('意见反馈功能开发中');
    // wx.navigateTo({
    //   url: '/pages/feedback/index'
    // });
  },

  /**
   * 打开关于页面
   */
  openAbout() {
    wx.showModal({
      title: '关于心安AI',
      content: '心安AI v1.0.0\n让焦虑变成行动力\n\n开发团队：心安AI团队',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡，用于弹窗内容区域
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadUserData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '心安AI - 让焦虑变成行动力',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '心安AI - 让焦虑变成行动力',
      imageUrl: '/images/share-cover.jpg'
    };
  }
});