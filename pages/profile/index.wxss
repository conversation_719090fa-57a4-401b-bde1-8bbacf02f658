/* pages/profile/index.wxss */

.profile-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding-bottom: var(--spacing-xl);
}

/* 用户信息卡片 */
.user-info-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  padding: var(--spacing-xl) var(--page-padding);
  margin-bottom: var(--component-margin);
}

.user-header {
  display: flex;
  align-items: flex-start;
}

.avatar-section {
  position: relative;
  margin-right: var(--spacing-lg);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-edit-btn {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  width: 48rpx;
  height: 48rpx;
  background: var(--bg-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
}

.edit-icon {
  width: 24rpx;
  height: 24rpx;
}

.user-details {
  flex: 1;
  color: #FFFFFF;
}

.user-name-section {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.user-name {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  margin-right: var(--spacing-sm);
}

.edit-name-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon-small {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.8;
}

.user-desc {
  font-size: var(--font-size-body-sm);
  opacity: 0.8;
  margin-bottom: var(--spacing-lg);
}

.user-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-caption);
  opacity: 0.8;
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 var(--spacing-lg);
}

/* 成就展示 */
.achievement-section {
  background: var(--bg-primary);
  margin-bottom: var(--component-margin);
  padding: var(--spacing-lg) 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--page-padding);
  margin-bottom: var(--spacing-lg);
}

.section-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.section-more {
  font-size: var(--font-size-body-sm);
  color: var(--primary-color);
}

.achievement-scroll {
  white-space: nowrap;
}

.achievement-list {
  display: flex;
  padding: 0 var(--page-padding);
}

.achievement-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: var(--spacing-lg);
  min-width: 120rpx;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.achievement-item.unlocked {
  background: rgba(74, 144, 226, 0.1);
}

.achievement-item.locked {
  opacity: 0.5;
}

.achievement-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-sm);
}

.achievement-emoji {
  font-size: 48rpx;
}

.achievement-name {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
  text-align: center;
}

/* 功能菜单 */
.menu-section {
  padding: 0 var(--page-padding);
}

.menu-group {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--component-margin);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--bg-tertiary);
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: var(--bg-secondary);
}

.menu-item.danger:active {
  background: rgba(231, 76, 60, 0.05);
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--border-radius-md);
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-lg);
}

.icon {
  width: 40rpx;
  height: 40rpx;
}

.danger-icon {
  filter: hue-rotate(0deg) saturate(1.5) brightness(0.8);
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.danger-text {
  color: var(--error-color);
}

.menu-desc {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
}

.menu-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

.menu-toggle {
  margin-left: var(--spacing-md);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  margin: var(--spacing-xl);
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal-overlay.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1rpx solid var(--bg-tertiary);
}

.modal-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 24rpx;
  height: 24rpx;
}

.modal-body {
  padding: var(--spacing-xl);
}

.nickname-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2rpx solid var(--bg-tertiary);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-input);
  margin-bottom: var(--spacing-sm);
}

.nickname-input:focus {
  border-color: var(--primary-color);
}

.input-tip {
  font-size: var(--font-size-caption);
  color: var(--text-tertiary);
}

.warning-text {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.warning-list {
  margin: var(--spacing-md) 0;
}

.warning-item {
  display: block;
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.modal-footer {
  display: flex;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1rpx solid var(--bg-tertiary);
  gap: var(--spacing-md);
}

.modal-footer .btn {
  flex: 1;
}
