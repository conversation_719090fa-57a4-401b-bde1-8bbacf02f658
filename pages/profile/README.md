# Profile 页面说明

## 功能概述

Profile页面是"心安AI"小程序的个人中心页面，提供用户信息管理、数据统计查看、设置配置等核心功能。

## 主要功能模块

### 1. 用户信息展示
- **头像管理**: 支持拍照或从相册选择头像，自动上传到云存储
- **昵称编辑**: 弹窗式昵称编辑，支持实时验证和保存
- **使用统计**: 展示连续使用天数、完成任务数、倾诉次数等关键指标

### 2. 成就系统
- **成就展示**: 横向滚动展示用户获得的成就徽章
- **解锁状态**: 区分已解锁和未解锁成就的视觉效果
- **成就详情**: 点击查看成就描述和获得条件

### 3. 功能设置
- **通知管理**: 
  - 开关控制推送通知
  - 自动请求订阅消息权限
  - 支持通知时间设置
- **主题设置**: 
  - 浅色/深色/跟随系统三种模式
  - 实时切换主题效果
- **隐私设置**: 
  - 数据保护选项
  - 隐私控制面板

### 4. 数据管理
- **数据统计**: 查看详细的使用数据和趋势分析
- **数据导出**: 
  - 导出个人数据备份
  - 支持JSON格式
  - 包含所有用户生成内容
- **数据删除**: 
  - 安全确认机制
  - 永久删除所有个人数据
  - 不可恢复操作

### 5. 帮助支持
- **帮助中心**: 使用指南和常见问题解答
- **意见反馈**: 用户建议和问题反馈渠道
- **关于页面**: 版本信息和开发团队介绍

## 技术实现

### 前端架构
- **WXML**: 语义化标签结构，支持无障碍访问
- **WXSS**: 响应式设计，适配不同屏幕尺寸
- **JavaScript**: 模块化代码组织，异步数据处理

### 后端服务
- **云函数**: `user` 云函数提供所有用户相关API
- **云存储**: 头像图片存储和管理
- **云数据库**: 用户信息、设置、统计数据存储

### 数据流程
```
用户操作 → 前端验证 → 云函数调用 → 数据库操作 → 结果返回 → 界面更新
```

## API 接口

### 用户统计 - getStats
```javascript
wx.cloud.callFunction({
  name: 'user',
  data: { action: 'getStats' }
})
```

### 获取成就 - getAchievements
```javascript
wx.cloud.callFunction({
  name: 'user',
  data: { action: 'getAchievements' }
})
```

### 获取设置 - getSettings
```javascript
wx.cloud.callFunction({
  name: 'user',
  data: { action: 'getSettings' }
})
```

### 更新设置 - updateSettings
```javascript
wx.cloud.callFunction({
  name: 'user',
  data: { 
    action: 'updateSettings',
    settings: { notificationEnabled: true }
  }
})
```

### 导出数据 - exportData
```javascript
wx.cloud.callFunction({
  name: 'user',
  data: { action: 'exportData' }
})
```

### 删除数据 - deleteAllData
```javascript
wx.cloud.callFunction({
  name: 'user',
  data: { action: 'deleteAllData' }
})
```

## 设计规范

### 视觉设计
- **色彩**: 遵循品牌色彩系统，主色调#4A90E2
- **字体**: 系统字体，层次分明的字号设置
- **间距**: 8rpx基础单位的间距系统
- **圆角**: 统一的圆角规范，提升视觉体验

### 交互设计
- **反馈**: 所有操作都有明确的成功/失败反馈
- **动画**: 流畅的过渡动画，提升用户体验
- **加载**: 异步操作显示加载状态
- **错误处理**: 友好的错误提示和恢复机制

### 无障碍支持
- **语义化**: 使用正确的HTML语义标签
- **对比度**: 确保文字和背景有足够对比度
- **触摸目标**: 最小88rpx的触摸区域
- **屏幕阅读器**: 支持aria标签和角色定义

## 使用说明

### 开发环境
1. 确保微信开发者工具已安装
2. 配置云开发环境
3. 部署相关云函数

### 测试流程
1. 在开发者工具中打开项目
2. 切换到Profile页面
3. 测试各项功能是否正常
4. 检查云函数调用和数据存储

### 部署上线
1. 完成功能测试
2. 上传云函数代码
3. 提交小程序代码审核
4. 发布正式版本

## 注意事项

### 隐私保护
- 所有用户数据加密存储
- 严格的数据访问权限控制
- 支持用户数据删除权利

### 性能优化
- 图片懒加载和压缩
- 数据分页加载
- 缓存策略优化

### 兼容性
- 支持微信7.0+版本
- 适配iOS和Android平台
- 响应式设计支持不同屏幕

## 更新日志

### v1.0.0 (2025-07-06)
- ✅ 完成基础页面结构
- ✅ 实现用户信息管理
- ✅ 添加设置功能模块
- ✅ 集成数据管理功能
- ✅ 完善错误处理机制
