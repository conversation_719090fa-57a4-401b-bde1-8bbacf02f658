<!--pages/mood-journal/index.wxml-->
<view class="mood-journal-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">心情日记</text>
      <text class="page-subtitle">记录每天的情绪变化</text>
    </view>
    <view class="header-actions">
      <button class="btn btn-primary btn-small" bindtap="createJournal">
        <image src="/images/icons/add.png" class="btn-icon" />
        记录
      </button>
    </view>
  </view>

  <!-- 今日心情快速记录 -->
  <view class="today-mood-section">
    <view class="section-header">
      <text class="section-title">今天感觉怎么样？</text>
      <text class="today-date">{{todayDate}}</text>
    </view>
    
    <view class="mood-selector">
      <view 
        class="mood-option {{selectedMood === item.value ? 'active' : ''}}"
        wx:for="{{moodOptions}}" 
        wx:key="value"
        bindtap="selectMood"
        data-mood="{{item.value}}"
      >
        <text class="mood-emoji">{{item.emoji}}</text>
        <text class="mood-name">{{item.name}}</text>
      </view>
    </view>
    
    <view class="mood-intensity" wx:if="{{selectedMood}}">
      <text class="intensity-label">情绪强度</text>
      <view class="intensity-slider">
        <slider 
          value="{{moodIntensity}}"
          min="1"
          max="10"
          step="1"
          show-value
          bindchange="onIntensityChange"
          activeColor="#4A90E2"
          backgroundColor="#E9ECEF"
        />
      </view>
      <view class="intensity-labels">
        <text class="intensity-text">轻微</text>
        <text class="intensity-text">强烈</text>
      </view>
    </view>
    
    <button 
      class="btn btn-primary quick-save-btn"
      bindtap="quickSaveMood"
      disabled="{{!selectedMood}}"
      wx:if="{{selectedMood && !todayJournal}}"
    >
      快速保存
    </button>
    
    <view class="today-journal-info" wx:if="{{todayJournal}}">
      <text class="journal-status">今日已记录</text>
      <button class="btn btn-secondary btn-small" bindtap="editTodayJournal">
        编辑详情
      </button>
    </view>
  </view>

  <!-- 情绪统计 -->
  <view class="mood-stats-section">
    <view class="section-header">
      <text class="section-title">最近7天</text>
      <text class="stats-summary">{{getStatsSummary()}}</text>
    </view>
    
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{moodStats.totalRecords || 0}}</text>
        <text class="stat-label">记录天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{moodStats.mostCommonMood || '-'}}</text>
        <text class="stat-label">主要情绪</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{getAverageIntensity()}}</text>
        <text class="stat-label">平均强度</text>
      </view>
    </view>
    
    <!-- 情绪趋势图 -->
    <view class="mood-trend">
      <text class="trend-title">情绪趋势</text>
      <view class="trend-chart">
        <view 
          class="trend-day {{item.hasRecord ? 'has-record' : ''}}"
          wx:for="{{moodTrend}}" 
          wx:key="date"
        >
          <view class="trend-dot {{item.mood}}" wx:if="{{item.hasRecord}}"></view>
          <text class="trend-date">{{formatTrendDate(item.date)}}</text>
        </view>
      </view>
      <view class="trend-legend">
        <view 
          class="legend-item"
          wx:for="{{moodOptions}}" 
          wx:key="value"
        >
          <view class="legend-dot {{item.value}}"></view>
          <text class="legend-text">{{item.name}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 历史记录 -->
  <view class="journal-history-section">
    <view class="section-header">
      <text class="section-title">历史记录</text>
      <button class="filter-btn" bindtap="showDatePicker">
        <image src="/images/icons/calendar.png" class="filter-icon" />
      </button>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-section" wx:if="{{isLoading}}">
      <view class="loading-animation">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
      </view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 日记列表 -->
    <view class="journal-list" wx:if="{{!isLoading}}">
      <view 
        class="journal-item"
        wx:for="{{journalList}}" 
        wx:key="_id"
        bindtap="viewJournal"
        data-id="{{item._id}}"
      >
        <view class="journal-date">
          <text class="date-day">{{formatJournalDate(item.recordDate, 'day')}}</text>
          <text class="date-month">{{formatJournalDate(item.recordDate, 'month')}}</text>
        </view>
        
        <view class="journal-content">
          <view class="journal-header">
            <view class="mood-info">
              <text class="mood-emoji">{{getMoodEmoji(item.mood.primary)}}</text>
              <text class="mood-text">{{item.mood.primary}}</text>
            </view>
            <text class="intensity-value">强度 {{item.mood.intensity}}/10</text>
          </view>
          
          <text class="journal-note" wx:if="{{item.note}}">{{item.note}}</text>
          
          <view class="journal-meta" wx:if="{{item.activities.length > 0 || item.tags.length > 0}}">
            <view class="activities" wx:if="{{item.activities.length > 0}}">
              <text class="meta-label">活动:</text>
              <text class="meta-value">{{getActivitiesText(item.activities)}}</text>
            </view>
            <view class="tags" wx:if="{{item.tags.length > 0}}">
              <text 
                class="tag"
                wx:for="{{item.tags}}" 
                wx:key="*this"
                wx:for-item="tag"
              >
                {{tag}}
              </text>
            </view>
          </view>
        </view>
        
        <view class="journal-actions">
          <button 
            class="action-btn"
            bindtap="editJournal"
            data-id="{{item._id}}"
            catchtap="true"
          >
            <image src="/images/icons/edit.png" class="action-icon" />
          </button>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{journalList.length === 0}}">
        <image src="/images/icons/journal.png" class="empty-icon" />
        <text class="empty-title">还没有心情记录</text>
        <text class="empty-desc">开始记录你的第一个心情日记吧</text>
        <button class="btn btn-primary" bindtap="createJournal">
          开始记录
        </button>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && !isLoading}}">
      <button class="btn btn-secondary btn-small" bindtap="loadMore" loading="{{isLoadingMore}}">
        {{isLoadingMore ? '加载中...' : '加载更多'}}
      </button>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 日期选择器 -->
<view class="date-picker-modal" wx:if="{{showDatePicker}}">
  <view class="modal-overlay" bindtap="hideDatePicker"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">选择日期范围</text>
      <button class="modal-close" bindtap="hideDatePicker">×</button>
    </view>
    
    <view class="modal-body">
      <view class="date-range">
        <view class="date-input">
          <text class="date-label">开始日期</text>
          <picker 
            mode="date"
            value="{{dateRange.start}}"
            bindchange="onStartDateChange"
          >
            <text class="date-value">{{dateRange.start || '选择日期'}}</text>
          </picker>
        </view>
        
        <view class="date-input">
          <text class="date-label">结束日期</text>
          <picker 
            mode="date"
            value="{{dateRange.end}}"
            bindchange="onEndDateChange"
          >
            <text class="date-value">{{dateRange.end || '选择日期'}}</text>
          </picker>
        </view>
      </view>
      
      <view class="quick-dates">
        <text class="quick-date-title">快速选择</text>
        <view class="quick-date-options">
          <button 
            class="quick-date-btn"
            wx:for="{{quickDateOptions}}" 
            wx:key="value"
            bindtap="selectQuickDate"
            data-value="{{item.value}}"
          >
            {{item.label}}
          </button>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="btn btn-secondary" bindtap="resetDateRange">重置</button>
      <button class="btn btn-primary" bindtap="applyDateRange">应用</button>
    </view>
  </view>
</view>
