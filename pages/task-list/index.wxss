/* pages/task-list/index.wxss */

.task-list-page {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--page-padding);
  background: var(--bg-primary);
  border-bottom: 1rpx solid var(--bg-tertiary);
}

.header-content {
  flex: 1;
}

.page-title {
  display: block;
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.task-count {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

.header-actions .btn-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: var(--spacing-xs);
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  align-items: center;
  background: var(--bg-primary);
  border-bottom: 1rpx solid var(--bg-tertiary);
  padding: var(--spacing-md) 0;
}

.filter-scroll {
  flex: 1;
  white-space: nowrap;
}

.filter-tabs {
  display: flex;
  padding: 0 var(--page-padding);
  gap: var(--spacing-lg);
}

.filter-tab {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-xl);
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
}

.filter-tab.active {
  color: var(--primary-color);
  background: rgba(74, 144, 226, 0.1);
  font-weight: var(--font-weight-medium);
}

.tab-count {
  font-size: var(--font-size-caption);
  margin-left: var(--spacing-xs);
}

.filter-actions {
  display: flex;
  gap: var(--spacing-sm);
  padding: 0 var(--page-padding);
}

.filter-btn {
  width: 64rpx;
  height: 64rpx;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.filter-btn::after {
  border: none;
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 任务内容区域 */
.task-content {
  flex: 1;
  padding: var(--spacing-md) 0;
}

.tasks-container {
  padding: 0 var(--page-padding);
}

/* 任务项 */
.task-item {
  display: flex;
  align-items: flex-start;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border-left: 4rpx solid var(--text-tertiary);
  transition: all 0.3s ease;
  position: relative;
}

.task-item:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-md);
}

.task-item.high {
  border-left-color: var(--error-color);
}

.task-item.medium {
  border-left-color: var(--warning-color);
}

.task-item.low {
  border-left-color: var(--success-color);
}

.task-item.completed {
  opacity: 0.6;
}

.task-checkbox {
  width: 48rpx;
  height: 48rpx;
  margin-right: var(--spacing-md);
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-icon {
  font-size: 32rpx;
  line-height: 1;
}

.checkbox-icon.checked {
  color: var(--success-color);
}

.checkbox-icon.unchecked {
  color: var(--text-tertiary);
}

.task-content-area {
  flex: 1;
  min-width: 0;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.task-title {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: 1.4;
  flex: 1;
  margin-right: var(--spacing-md);
}

.task-title.completed {
  text-decoration: line-through;
  color: var(--text-secondary);
}

.task-meta {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.priority-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-caption);
  font-weight: var(--font-weight-medium);
}

.priority-tag.high {
  background: rgba(231, 76, 60, 0.1);
  color: var(--error-color);
}

.priority-tag.medium {
  background: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
}

.priority-tag.low {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

.category-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-caption);
}

.task-description {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.task-time {
  display: flex;
  align-items: center;
}

.time-icon {
  font-size: 20rpx;
  margin-right: var(--spacing-xs);
  opacity: 0.6;
}

.time-text {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

.task-progress {
  font-size: var(--font-size-caption);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.task-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.task-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(74, 144, 226, 0.1);
  color: var(--primary-color);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-caption);
}

.task-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-left: var(--spacing-md);
}

.action-btn {
  width: 56rpx;
  height: 56rpx;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.action-btn::after {
  border: none;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.active {
  background: var(--primary-color);
}

.action-btn.active .action-icon {
  color: #FFFFFF;
}

.action-icon {
  font-size: 24rpx;
  line-height: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--spacing-xl);
  opacity: 0.5;
  display: block;
}

.empty-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-desc {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  padding: var(--spacing-lg);
}

/* 弹窗样式 */
.filter-modal,
.sort-modal,
.action-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  margin: var(--spacing-xl);
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1rpx solid var(--bg-tertiary);
}

.modal-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  background: none;
  border: none;
  font-size: 32rpx;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: var(--spacing-lg) var(--spacing-xl);
}

.filter-group {
  margin-bottom: var(--spacing-xl);
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-group-title {
  display: block;
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.filter-option {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-body-sm);
  transition: all 0.3s ease;
}

.filter-option.active {
  background: var(--primary-color);
  color: #FFFFFF;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--bg-tertiary);
  transition: background-color 0.3s ease;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option:active {
  background: var(--bg-secondary);
}

.sort-option.active {
  background: rgba(74, 144, 226, 0.1);
}

.sort-text {
  font-size: var(--font-size-body);
  color: var(--text-primary);
}

.sort-check {
  width: 32rpx;
  height: 32rpx;
}

.modal-footer {
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  border-top: 1rpx solid var(--bg-tertiary);
  display: flex;
  gap: var(--spacing-md);
}

.modal-footer .btn {
  flex: 1;
}

/* 操作弹窗 */
.action-modal .modal-content {
  margin: var(--spacing-xl) var(--spacing-xl) auto;
  border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
  animation: slideUpFromBottom 0.3s ease-out;
}

.action-list {
  padding: var(--spacing-lg) 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  transition: background-color 0.3s ease;
}

.action-item:active {
  background: var(--bg-secondary);
}

.action-item.danger {
  color: var(--error-color);
}

.action-item .action-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: var(--spacing-lg);
}

.action-text {
  font-size: var(--font-size-body);
  color: inherit;
}

/* 动画 */
@keyframes slideUpFromBottom {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
